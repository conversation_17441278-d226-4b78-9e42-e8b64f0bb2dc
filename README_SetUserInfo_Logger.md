# SetUserInfo FCM Token Logger - Frida Script

This Frida script comprehensively monitors and logs all SetUserInfo protocol buffer usage in the Shopee Partner VN app, specifically focusing on FCM token registration during user login flows.

## Features

### 🔍 **Comprehensive Monitoring**
- **SetUserInfo Protocol Buffer Creation**: Tracks all SetUserInfo builder instances and field assignments
- **FCM Token Registration**: Monitors FCM token setting and machine code assignments
- **Network Communication**: Logs TCP packet creation and transmission (command 0x43)
- **Login Flow Integration**: Tracks login success callbacks and FCM registration triggers
- **Response Processing**: Monitors server responses to SetUserInfo requests

### 📊 **Detailed Logging**
- **Color-coded output** for easy identification of different events
- **Stack traces** for SetUserInfo builder creation to identify call origins
- **Protocol buffer field extraction** including FCM tokens and machine codes
- **TCP packet analysis** with payload decoding
- **Real-time state monitoring** of user login status and stored tokens

### 🎯 **Key Monitoring Points**

1. **SetUserInfo Builder Hooks**:
   - Constructor calls with stack traces
   - FCM token setting (`pn_token` field)
   - Machine code setting (`machine_code` field)
   - Request ID assignment
   - Protocol buffer building

2. **Network Request Classes**:
   - Class Z: Main FCM token registration
   - Class X: GPNS token registration  
   - Class Y: Login-specific registration
   - Class C0: Push notification options

3. **Network Communication**:
   - TCP packet creation (command 0x43)
   - Network manager transmission
   - Response processing and parsing

4. **Login Flow Integration**:
   - Login processor callbacks
   - ToC/ToB login success handlers
   - FCM service token updates
   - User authentication state changes

5. **Data Storage**:
   - Device store FCM token storage
   - Machine code persistence
   - User info updates

## Usage

### Prerequisites
- Frida installed and configured
- Shopee Partner VN app installed on target device
- USB debugging enabled or network access to device

### Basic Usage

```bash
# Run the script on USB-connected device
frida -U -f com.shopee.vn.partner -l setuserinfo_logger.js --no-pause

# Run on specific device by ID
frida -D <device_id> -f com.shopee.vn.partner -l setuserinfo_logger.js --no-pause

# Attach to running process
frida -U com.shopee.vn.partner -l setuserinfo_logger.js
```

### Advanced Usage

```bash
# Save output to file
frida -U -f com.shopee.vn.partner -l setuserinfo_logger.js --no-pause > fcm_logs.txt

# Run with additional debugging
frida -U -f com.shopee.vn.partner -l setuserinfo_logger.js --no-pause --debug
```

## Output Examples

### FCM Token Registration
```
[SUCCESS] FCM_TOKEN: Setting FCM token: eK7_vQoR8kE:APA91bH...
    Token length: 163
[SUCCESS] MACHINE_CODE: Setting machine code: android_gcm
[INFO] REQUEST_ID: Setting request ID: 1640995200123_456
[SUCCESS] BUILD: Building SetUserInfo protocol buffer
    FCM Token: eK7_vQoR8kE:APA91bH...
    Machine Code: android_gcm
    Request ID: 1640995200123_456
```

### Network Transmission
```
[SUCCESS] TCP_PACKET: Creating SetUserInfo TCP packet
    Command: 0x43
    Payload size: 245 bytes
    Decoded SetUserInfo:
      FCM Token: eK7_vQoR8kE:APA91bH...
      Machine Code: android_gcm
      Request ID: 1640995200123_456
[SUCCESS] NETWORK: Sending SetUserInfo TCP packet (0x43)
    Request ID: 1640995200123_456
    User ID: user_12345
    Packet size: 245 bytes
```

### Login Flow
```
[SUCCESS] LOGIN_SUCCESS: ToC Login success - sending user data
    This will trigger FCM token registration
[SUCCESS] USER_ID: User ID set: 12345678
[SUCCESS] USER_AUTH: User authentication token set
    Token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
[SUCCESS] FCM_REGISTER: FCM Token Registration Called
    Push Token: eK7_vQoR8kE:APA91bH...
    Machine Code: android_gcm
    User logged in: true
    User ID: 12345678
```

### Server Response
```
[SUCCESS] RESPONSE: SetUserInfo response received
    Response length: 89 bytes
    Request ID: 1640995200123_456
    Error code: 0
    Success: true
```

## Interactive Commands

While the script is running, you can use these commands in the Frida console:

```javascript
// Print current state summary
printCurrentState()

// Manual state check
Java.perform(function() {
    const app = Java.use("com.shopee.app.application.ShopeeApplication").get();
    const userInfo = app.getComponent().loggedInUser();
    console.log("Logged in: " + userInfo.isLoggedIn());
});
```

## Troubleshooting

### Common Issues

1. **Hook Installation Failures**:
   - Some hooks may fail if classes are not loaded yet
   - The script continues with warnings for failed hooks
   - Try triggering the relevant functionality to load classes

2. **Permission Issues**:
   - Ensure proper Frida server permissions
   - Check USB debugging authorization
   - Verify app is debuggable or use appropriate bypass

3. **Class Not Found**:
   - Some classes may be obfuscated differently in different versions
   - Check the actual class names in your target APK
   - Modify the script accordingly

### Debugging Tips

1. **Enable Stack Traces**: The script automatically shows stack traces for SetUserInfo builder creation
2. **Monitor Specific Events**: Focus on specific log tags using grep:
   ```bash
   frida -U com.shopee.vn.partner -l setuserinfo_logger.js | grep "FCM_TOKEN"
   ```
3. **State Monitoring**: Use the periodic state summary to track changes over time

## Security Considerations

⚠️ **Important**: This script is for security research and educational purposes only.

- Only use on applications you own or have explicit permission to test
- FCM tokens are sensitive data - handle appropriately
- Be aware of local privacy laws and regulations
- Do not use for malicious purposes

## Customization

The script can be easily customized for different needs:

1. **Add New Hooks**: Extend the script to monitor additional classes or methods
2. **Filter Output**: Modify logging functions to focus on specific events
3. **Export Data**: Add functionality to export logs in different formats
4. **Real-time Analysis**: Integrate with external tools for real-time analysis

## Version Compatibility

This script is designed for:
- **Target App**: Shopee Partner VN 3.23.0
- **Frida Version**: 16.0+ recommended
- **Android Version**: 7.0+ (API level 24+)

For different app versions, you may need to:
- Update class names if obfuscation changes
- Adjust method signatures
- Verify field names in protocol buffers

## Contributing

To improve this script:
1. Test on different app versions
2. Add support for additional push notification services
3. Enhance error handling and recovery
4. Improve output formatting and analysis capabilities

---

**Happy Hunting! 🔍**
