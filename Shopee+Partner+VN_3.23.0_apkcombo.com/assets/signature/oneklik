-----BEGIN CERTIFICATE-----
MIIFTzCCBDegAwIBAgIQAzoU1qGQ3YVG5Zw6vA6GcDANBgkqhkiG9w0BAQsFADBN
MQswCQYDVQQGEwJVUzEVMBMGA1UEChMMRGlnaUNlcnQgSW5jMScwJQYDVQQDEx5E
aWdpQ2VydCBTSEEyIFNlY3VyZSBTZXJ2ZXIgQ0EwHhcNMTYxMDE5MDAwMDAwWhcN
MTgxMDI0MTIwMDAwWjCBmzELMAkGA1UEBhMCSUQxFDASBgNVBAgTC0RLSSBKYWth
cnRhMRAwDgYDVQQHEwdKYWthcnRhMSEwHwYDVQQKExhQVCBCYW5rIENlbnRyYWwg
QXNpYSBUYmsxIzAhBgNVBAsTGkRpdmlzaSBUZWtub2xvZ2kgSW5mb3JtYXNpMRww
GgYDVQQDExNvbmVrbGlrLmtsaWtiY2EuY29tMIIBIjANBgkqhkiG9w0BAQEFAAOC
AQ8AMIIBCgKCAQEAp25+MOxYFA0WTq2nE5aH82JoqS8fLAwIQ9+sh8KhE7MtO0SP
8QPs1BnEwfTyEwCDtDVjGoJn5iqI+18BcIPdrRNst1oqVTL38bObT7npjsa9E5qw
Zk+KW04r3rDE0bADiZcIxPtHW5KNnV4GBUPaxgB4IAtOrJJSLtiek74bHRaGE2al
6hC3yQKNaWz/g7ocVg19+vSxeOeioJEGmFY3Ky9Oyj43iceGsEJpxPlihjrG6sqg
YxyByaCT3Wryta/LdFQrzErjbmOawqrp3xD9EZZy/ADV7QfDizhv/ETpbi0U0lyA
rgYk3Y/PCo1Szbg5n2qhRiHYe2oi/iuKLXHslwIDAQABo4IB2jCCAdYwHwYDVR0j
BBgwFoAUD4BhHIIxYdUvKOeNRji0LOHG2eIwHQYDVR0OBBYEFCtU6x9P4DvcRcEX
t57lmipHtO+EMB4GA1UdEQQXMBWCE29uZWtsaWsua2xpa2JjYS5jb20wDgYDVR0P
AQH/BAQDAgWgMB0GA1UdJQQWMBQGCCsGAQUFBwMBBggrBgEFBQcDAjBrBgNVHR8E
ZDBiMC+gLaArhilodHRwOi8vY3JsMy5kaWdpY2VydC5jb20vc3NjYS1zaGEyLWc1
LmNybDAvoC2gK4YpaHR0cDovL2NybDQuZGlnaWNlcnQuY29tL3NzY2Etc2hhMi1n
NS5jcmwwTAYDVR0gBEUwQzA3BglghkgBhv1sAQEwKjAoBggrBgEFBQcCARYcaHR0
cHM6Ly93d3cuZGlnaWNlcnQuY29tL0NQUzAIBgZngQwBAgIwfAYIKwYBBQUHAQEE
cDBuMCQGCCsGAQUFBzABhhhodHRwOi8vb2NzcC5kaWdpY2VydC5jb20wRgYIKwYB
BQUHMAKGOmh0dHA6Ly9jYWNlcnRzLmRpZ2ljZXJ0LmNvbS9EaWdpQ2VydFNIQTJT
ZWN1cmVTZXJ2ZXJDQS5jcnQwDAYDVR0TAQH/BAIwADANBgkqhkiG9w0BAQsFAAOC
AQEAJJluRm4cGtHqjSLokoPgw51ptJjW1KznkuxlR9jnpNs7Qeptog+0Z+JiGfz+
CFG7by4HR9Cvafw8pwucGDVTGlVzbIZJz6rke8Oxx5vBoSNbs98VsFBc5GijzxMR
BJMLTE3nDMH90TDVppGyA0JVjUZViqGHbtiDWdxezS2utSmKG+6G/AMYt7NJPH0Z
Ujk5yjvRY20/BMcq5sDTfAli5BwaxqJvCbcsl5Huuyb1R67FKCqn8U/qOKMpQq2q
z6/RhSs7l+sjKWpCHyAsiySCKweKLc94gxI7XYHWp6DZGi4eGKW3u+vUrnnSuo4d
AO/wJxKURMYxkGCejKD/g+sAbA==
-----END CERTIFICATE-----
