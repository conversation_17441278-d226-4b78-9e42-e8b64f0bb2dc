#!/usr/bin/env python3
"""
SetUserInfo Log Analyzer
Analyzes logs generated by the setuserinfo_logger.js Frida script
Author: Security Researcher
"""

import re
import json
import argparse
from datetime import datetime
from collections import defaultdict, Counter
import sys

class SetUserInfoLogAnalyzer:
    def __init__(self, log_file):
        self.log_file = log_file
        self.events = []
        self.fcm_tokens = set()
        self.machine_codes = Counter()
        self.request_ids = set()
        self.user_sessions = []
        
    def parse_logs(self):
        """Parse the Frida log file and extract structured data"""
        print(f"[+] Parsing log file: {self.log_file}")
        
        try:
            with open(self.log_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
        except FileNotFoundError:
            print(f"[-] Error: Log file '{self.log_file}' not found")
            return False
        except Exception as e:
            print(f"[-] Error reading log file: {e}")
            return False
            
        current_event = {}
        
        for line_num, line in enumerate(lines, 1):
            line = line.strip()
            if not line:
                continue
                
            # Parse different log types
            event = self._parse_log_line(line, line_num)
            if event:
                self.events.append(event)
                self._extract_data(event)
                
        print(f"[+] Parsed {len(self.events)} events from {len(lines)} lines")
        return True
        
    def _parse_log_line(self, line, line_num):
        """Parse a single log line and extract event data"""
        # Remove ANSI color codes
        line = re.sub(r'\x1b\[[0-9;]*m', '', line)
        
        # Match log patterns
        patterns = {
            'fcm_token': r'\[SUCCESS\] FCM_TOKEN: Setting FCM token: (.+)',
            'machine_code': r'\[SUCCESS\] MACHINE_CODE: Setting machine code: (.+)',
            'request_id': r'\[INFO\] REQUEST_ID: Setting request ID: (.+)',
            'tcp_packet': r'\[SUCCESS\] TCP_PACKET: Creating SetUserInfo TCP packet',
            'network_send': r'\[SUCCESS\] NETWORK: Sending SetUserInfo TCP packet \(0x43\)',
            'response': r'\[SUCCESS\] RESPONSE: SetUserInfo response received',
            'login_success': r'\[SUCCESS\] LOGIN_SUCCESS: (.+)',
            'user_id': r'\[SUCCESS\] USER_ID: User ID set: (.+)',
            'user_auth': r'\[SUCCESS\] USER_AUTH: User authentication token set',
            'fcm_register': r'\[SUCCESS\] FCM_REGISTER: FCM Token Registration Called',
            'storage': r'\[SUCCESS\] STORAGE: (.+)',
            'new_token': r'\[SUCCESS\] FCM_NEW_TOKEN: New FCM token received'
        }
        
        for event_type, pattern in patterns.items():
            match = re.search(pattern, line)
            if match:
                event = {
                    'type': event_type,
                    'line_num': line_num,
                    'timestamp': datetime.now().isoformat(),
                    'raw_line': line
                }
                
                if match.groups():
                    event['data'] = match.group(1)
                    
                # Extract additional data from the line
                self._extract_additional_data(event, line)
                return event
                
        return None
        
    def _extract_additional_data(self, event, line):
        """Extract additional data from log lines"""
        # Extract token length
        token_length_match = re.search(r'Token length: (\d+)', line)
        if token_length_match:
            event['token_length'] = int(token_length_match.group(1))
            
        # Extract payload size
        payload_size_match = re.search(r'Payload size: (\d+) bytes', line)
        if payload_size_match:
            event['payload_size'] = int(payload_size_match.group(1))
            
        # Extract error code
        error_code_match = re.search(r'Error code: (\d+)', line)
        if error_code_match:
            event['error_code'] = int(error_code_match.group(1))
            
        # Extract success status
        if 'Success: true' in line:
            event['success'] = True
        elif 'Success: false' in line:
            event['success'] = False
            
    def _extract_data(self, event):
        """Extract and store relevant data from events"""
        if event['type'] == 'fcm_token' and 'data' in event:
            self.fcm_tokens.add(event['data'])
            
        elif event['type'] == 'machine_code' and 'data' in event:
            self.machine_codes[event['data']] += 1
            
        elif event['type'] == 'request_id' and 'data' in event:
            self.request_ids.add(event['data'])
            
    def analyze_fcm_tokens(self):
        """Analyze FCM token patterns"""
        print("\n" + "="*60)
        print("FCM TOKEN ANALYSIS")
        print("="*60)
        
        print(f"Total unique FCM tokens: {len(self.fcm_tokens)}")
        
        if self.fcm_tokens:
            # Analyze token patterns
            token_lengths = [len(token) for token in self.fcm_tokens]
            print(f"Token length range: {min(token_lengths)} - {max(token_lengths)} characters")
            
            # Show token prefixes (first 20 chars for privacy)
            print("\nToken prefixes (first 20 characters):")
            for i, token in enumerate(sorted(self.fcm_tokens), 1):
                prefix = token[:20] + "..." if len(token) > 20 else token
                print(f"  {i}. {prefix}")
                
            # Analyze token structure
            print("\nToken structure analysis:")
            for token in self.fcm_tokens:
                parts = token.split(':')
                print(f"  Token parts: {len(parts)}")
                if len(parts) >= 2:
                    print(f"    Prefix: {parts[0]}")
                    print(f"    Main part length: {len(parts[1])}")
                break  # Just analyze first token as example
                
    def analyze_machine_codes(self):
        """Analyze machine code distribution"""
        print("\n" + "="*60)
        print("MACHINE CODE ANALYSIS")
        print("="*60)
        
        print("Machine code distribution:")
        for code, count in self.machine_codes.most_common():
            percentage = (count / sum(self.machine_codes.values())) * 100
            print(f"  {code}: {count} times ({percentage:.1f}%)")
            
        # Explain machine codes
        explanations = {
            'android_gcm': 'Firebase Cloud Messaging (FCM)',
            'android_hms': 'Huawei Mobile Services (HMS)',
            'android_gpns': 'Garena Push Notification Service (GPNS)'
        }
        
        print("\nMachine code explanations:")
        for code in self.machine_codes.keys():
            explanation = explanations.get(code, 'Unknown service')
            print(f"  {code}: {explanation}")
            
    def analyze_request_flow(self):
        """Analyze the request flow patterns"""
        print("\n" + "="*60)
        print("REQUEST FLOW ANALYSIS")
        print("="*60)
        
        # Group events by type
        event_counts = Counter(event['type'] for event in self.events)
        
        print("Event type distribution:")
        for event_type, count in event_counts.most_common():
            print(f"  {event_type}: {count}")
            
        # Analyze request-response pairs
        requests = [e for e in self.events if e['type'] in ['tcp_packet', 'network_send']]
        responses = [e for e in self.events if e['type'] == 'response']
        
        print(f"\nRequest-Response analysis:")
        print(f"  Total requests: {len(requests)}")
        print(f"  Total responses: {len(responses)}")
        
        if responses:
            successful_responses = sum(1 for r in responses if r.get('success', False))
            print(f"  Successful responses: {successful_responses}")
            print(f"  Success rate: {(successful_responses/len(responses)*100):.1f}%")
            
    def analyze_login_sessions(self):
        """Analyze login session patterns"""
        print("\n" + "="*60)
        print("LOGIN SESSION ANALYSIS")
        print("="*60)
        
        login_events = [e for e in self.events if 'login' in e['type'].lower()]
        auth_events = [e for e in self.events if e['type'] in ['user_id', 'user_auth']]
        
        print(f"Login-related events: {len(login_events)}")
        print(f"Authentication events: {len(auth_events)}")
        
        # Analyze login flow sequence
        print("\nLogin flow sequence:")
        login_sequence = []
        for event in self.events:
            if event['type'] in ['login_success', 'user_id', 'user_auth', 'fcm_register']:
                login_sequence.append(event['type'])
                
        if login_sequence:
            print("  Typical sequence:")
            for i, step in enumerate(login_sequence[:10], 1):  # Show first 10 steps
                print(f"    {i}. {step}")
                
    def analyze_timing_patterns(self):
        """Analyze timing patterns in the logs"""
        print("\n" + "="*60)
        print("TIMING ANALYSIS")
        print("="*60)
        
        # Group events by type and analyze gaps
        fcm_events = [e for e in self.events if 'fcm' in e['type'].lower()]
        network_events = [e for e in self.events if e['type'] in ['tcp_packet', 'network_send', 'response']]
        
        print(f"FCM-related events: {len(fcm_events)}")
        print(f"Network events: {len(network_events)}")
        
        # Analyze event clustering
        if len(self.events) > 1:
            print(f"\nEvent distribution:")
            print(f"  Total events: {len(self.events)}")
            print(f"  Line span: {self.events[0]['line_num']} - {self.events[-1]['line_num']}")
            
    def generate_summary_report(self):
        """Generate a comprehensive summary report"""
        print("\n" + "="*60)
        print("SUMMARY REPORT")
        print("="*60)
        
        print(f"Log file: {self.log_file}")
        print(f"Total events parsed: {len(self.events)}")
        print(f"Unique FCM tokens: {len(self.fcm_tokens)}")
        print(f"Unique request IDs: {len(self.request_ids)}")
        print(f"Machine codes used: {list(self.machine_codes.keys())}")
        
        # Security observations
        print("\nSecurity Observations:")
        
        if len(self.fcm_tokens) > 1:
            print("  ⚠️  Multiple FCM tokens detected - possible token refresh")
            
        if 'android_gcm' in self.machine_codes and 'android_hms' in self.machine_codes:
            print("  ℹ️  Both FCM and HMS support detected")
            
        if any(e.get('error_code', 0) != 0 for e in self.events):
            print("  ⚠️  Some requests failed - check error codes")
        else:
            print("  ✅ All requests appear successful")
            
        # Privacy considerations
        print("\nPrivacy Considerations:")
        print("  📱 FCM tokens can be used to track devices")
        print("  🔒 Tokens should be transmitted over secure channels")
        print("  🔄 Token rotation indicates good security practices")
        
    def export_json(self, output_file):
        """Export analysis results to JSON"""
        data = {
            'summary': {
                'total_events': len(self.events),
                'unique_fcm_tokens': len(self.fcm_tokens),
                'unique_request_ids': len(self.request_ids),
                'machine_codes': dict(self.machine_codes)
            },
            'fcm_tokens': list(self.fcm_tokens),
            'events': self.events
        }
        
        try:
            with open(output_file, 'w') as f:
                json.dump(data, f, indent=2)
            print(f"\n[+] Analysis results exported to: {output_file}")
        except Exception as e:
            print(f"[-] Error exporting to JSON: {e}")
            
    def run_analysis(self):
        """Run complete analysis"""
        if not self.parse_logs():
            return False
            
        self.analyze_fcm_tokens()
        self.analyze_machine_codes()
        self.analyze_request_flow()
        self.analyze_login_sessions()
        self.analyze_timing_patterns()
        self.generate_summary_report()
        
        return True

def main():
    parser = argparse.ArgumentParser(description='Analyze SetUserInfo Frida logs')
    parser.add_argument('log_file', help='Path to the Frida log file')
    parser.add_argument('--json', help='Export results to JSON file')
    parser.add_argument('--verbose', '-v', action='store_true', help='Verbose output')
    
    args = parser.parse_args()
    
    analyzer = SetUserInfoLogAnalyzer(args.log_file)
    
    if analyzer.run_analysis():
        if args.json:
            analyzer.export_json(args.json)
    else:
        sys.exit(1)

if __name__ == '__main__':
    main()
