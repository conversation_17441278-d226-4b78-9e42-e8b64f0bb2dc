# FCM Token Registration Analysis Report

## Summary of Captured Data

Based on the Frida script output, here's a comprehensive analysis of how the Shopee Partner app sends FCM tokens to the server during user login:

## 🔍 **Key Findings**

### **1. FCM Token Details**
- **Token**: `cMn3XrNXRjaU-L8ughSO46:APA91bETYk0dC4AbsNq18T3h2O2p7mV5wqyOMID46BYEbnkWeTmZIIv1BdZA0FqKc04FCNOoiepz4z8herc3v23KhcpoKFdk9hQ6Qoij_rx7OfpjXZCDIgc`
- **Length**: 142 characters
- **Format**: Standard Firebase FCM token format (`[prefix]:[main_token]`)
- **Platform**: `android_gcm` (Firebase Cloud Messaging)

### **2. User Information**
- **User ID**: `1267054343` (logged-in user)
- **Network User ID**: `4449183980344904348` (network session)
- **Request ID**: `3073045126856263819` (unique request identifier)

### **3. Device Information**
- **Brand**: OPPO
- **Model**: CPH1911
- **OS Version**: Android 29 (Android 10)
- **Manufacturer**: OPPO
- **Device String**: `Brand/oppo Model/cph1911 OSVer/29 Manufacturer/OPPO`

## 📊 **Network Communication Flow**

### **Step 1: Initial FCM Registration**
```
[SUCCESS] FCM_REGISTER: FCM Token Registration Called
    Push Token: cMn3XrNXRjaU-L8ughSO46:APA91bE...
    Machine Code: android_gcm
    User logged in: true
    User ID: 1267054343
```

### **Step 2: Token Storage**
```
[SUCCESS] STORAGE: Storing FCM token in device store
    Token: cMn3XrNXRjaU-L8ughSO46:APA91bE...
[INFO] STORAGE: Storing machine code: android_gcm
```

### **Step 3: SetUserInfo Request Creation**
```
[INFO] REQUEST_Z: Creating SetUserInfo request class z
[SUCCESS] PARAMS_Z: Setting parameters for request class z
    Parameters count: 8
    Param[0]: ZMhSYRBx+88gMK0j73OPKVie8d0RWL5XZ1sCQk0I61M= (Base64 encoded data)
    Param[1]: vi (Country/Language code)
    Param[2]: 101,97,98,102,... (Device identifier bytes)
    Param[3]: Brand/oppo Model/cph1911 OSVer/29 Manufacturer/OPPO
    Param[4]: false (Boolean flag)
    Param[5]: 101,97,98,102,... (Device identifier bytes - duplicate)
    Param[6]: cMn3XrNXRjaU-L8ughSO46:APA91bE... (FCM Token)
    Param[7]: android_gcm (Machine code)
```

### **Step 4: Protocol Buffer Creation**
```
[INFO] SetUserInfo.Builder: New SetUserInfo Builder created (default constructor)
[INFO] REQUEST_ID: Setting request ID: 3073045126856263819
[SUCCESS] FCM_TOKEN: Setting FCM token: cMn3XrNXRjaU-L8ughSO46:APA91bE...
    Token length: 142
[SUCCESS] MACHINE_CODE: Setting machine code: android_gcm
```

### **Step 5: TCP Packet Transmission**
```
[SUCCESS] TCP_PACKET: Creating SetUserInfo TCP packet
    Command: 0x43
    Payload size: 346 bytes
[SUCCESS] NETWORK: Sending SetUserInfo TCP packet (0x43)
    Request ID: CMD_SET_USER_INFO
    User ID: 3073045126856263819
    Packet size: 346 bytes
```

### **Step 6: Server Response**
```
[SUCCESS] RESPONSE: SetUserInfo response received
    Response length: 61 bytes
```

## 🔧 **Technical Details**

### **Protocol Buffer Structure**
The SetUserInfo protocol buffer contains:
- **pn_token**: FCM token as ByteString (field tag 0xd/13)
- **machine_code**: Platform identifier string
- **requestid**: Unique request identifier
- **ext**: Device extension information
- **token**: User authentication token

### **Network Protocol**
- **Protocol**: Custom TCP over persistent connection
- **Command**: 0x43 (67 decimal) for SetUserInfo
- **Packet Structure**:
  ```
  [0-3]: Payload Length (4 bytes, little-endian)
  [4]:   Command ID (0x43)
  [5+]:  Protocol Buffer Data
  ```

### **Request Parameters Analysis**
1. **Param[0]**: `ZMhSYRBx+88gMK0j73OPKVie8d0RWL5XZ1sCQk0I61M=`
   - Base64 encoded authentication/session data
   
2. **Param[1]**: `vi`
   - Country/language code (Vietnam)
   
3. **Param[2] & Param[5]**: `101,97,98,102,48,49,54,99,101,102,52,100,50,53,48,98,95,117,110,107,110,111,119,110`
   - Device identifier bytes
   - Decoded: `eabf016cef4d250b_unknown`
   
4. **Param[3]**: Device information string
   
5. **Param[4]**: `false` - Boolean configuration flag
   
6. **Param[6]**: FCM token (full token)
   
7. **Param[7]**: Machine code identifier

## 🚨 **Security Observations**

### **Data Transmission**
- FCM tokens are transmitted in plaintext within the encrypted TCP connection
- Device identifiers are included for tracking/association
- User authentication tokens are properly included

### **Token Management**
- Tokens are stored locally in device store
- Machine code properly identifies the push service type
- Request IDs ensure proper request-response matching

### **Privacy Implications**
- FCM tokens can be used to track devices across sessions
- Device information is transmitted (brand, model, OS version)
- User ID is associated with the FCM token on the server

## 📈 **Registration Success Indicators**

1. ✅ **FCM Token Generated**: 142-character Firebase token
2. ✅ **User Authentication**: User logged in (ID: 1267054343)
3. ✅ **Local Storage**: Token stored in device store
4. ✅ **Protocol Buffer Creation**: SetUserInfo built successfully
5. ✅ **Network Transmission**: TCP packet sent (346 bytes)
6. ✅ **Server Response**: Response received (61 bytes)

## 🔄 **Multiple Registration Attempts**

The logs show multiple SetUserInfo requests:
1. **First Request**: 201 bytes payload
2. **Second Request**: 346 bytes payload (with full FCM token)

This suggests:
- Initial registration attempt (possibly without full token data)
- Full registration with complete FCM token and device information

## 📝 **Recommendations for Further Analysis**

1. **Decrypt Response**: Analyze the 61-byte server response content
2. **Monitor Token Refresh**: Track FCM token updates over time
3. **Cross-Platform Testing**: Test with HMS (Huawei) and GPNS variants
4. **Session Correlation**: Track how tokens are used across app sessions
5. **Server-Side Analysis**: Monitor server logs for token processing

## 🎯 **Conclusion**

The Shopee Partner app successfully implements FCM token registration with proper:
- Token generation and storage
- User authentication integration
- Network protocol compliance
- Device information association
- Server communication

The registration flow follows standard practices while maintaining security through encrypted TCP connections and proper authentication token inclusion.
