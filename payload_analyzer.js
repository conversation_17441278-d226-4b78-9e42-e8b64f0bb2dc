/**
 * TCP Payload Analyzer for SetUserInfo packets
 * Extracts FCM tokens and other data from raw TCP payloads
 * Author: Security Researcher
 */

Java.perform(function() {
    console.log("[+] Starting TCP Payload Analyzer...");
    
    // Utility functions
    function bytesToHex(bytes) {
        let hex = "";
        for (let i = 0; i < bytes.length; i++) {
            hex += ("0" + (bytes[i] & 0xFF).toString(16)).slice(-2) + " ";
        }
        return hex.trim();
    }
    
    function bytesToString(bytes, encoding) {
        try {
            return Java.use("java.lang.String").$new(bytes, encoding || "UTF-8");
        } catch (e) {
            return "Error converting bytes: " + e.message;
        }
    }
    
    function findFCMToken(data) {
        try {
            const str = bytesToString(data);
            // FCM token pattern: prefix:APA91b[base64-like characters]
            const fcmRegex = /[A-Za-z0-9_-]{10,}:APA91b[A-Za-z0-9_-]{100,}/g;
            const matches = str.match(fcmRegex);
            return matches ? matches[0] : null;
        } catch (e) {
            return null;
        }
    }
    
    function findMachineCode(data) {
        try {
            const str = bytesToString(data);
            if (str.includes("android_gcm")) return "android_gcm";
            if (str.includes("android_hms")) return "android_hms";
            if (str.includes("android_gpns")) return "android_gpns";
            return null;
        } catch (e) {
            return null;
        }
    }
    
    function analyzeProtocolBuffer(payload) {
        console.log("    === Protocol Buffer Analysis ===");
        
        // Basic protobuf structure analysis
        let offset = 0;
        let fieldCount = 0;
        
        while (offset < payload.length && fieldCount < 20) {
            try {
                // Read varint tag
                let tag = 0;
                let shift = 0;
                let byte;
                
                do {
                    if (offset >= payload.length) break;
                    byte = payload[offset++] & 0xFF;
                    tag |= (byte & 0x7F) << shift;
                    shift += 7;
                } while ((byte & 0x80) !== 0);
                
                if (tag === 0) break;
                
                const fieldNumber = tag >>> 3;
                const wireType = tag & 0x7;
                
                console.log("      Field " + fieldNumber + " (wire type " + wireType + "):");
                
                if (wireType === 0) { // Varint
                    let value = 0;
                    shift = 0;
                    do {
                        if (offset >= payload.length) break;
                        byte = payload[offset++] & 0xFF;
                        value |= (byte & 0x7F) << shift;
                        shift += 7;
                    } while ((byte & 0x80) !== 0);
                    console.log("        Varint: " + value);
                    
                } else if (wireType === 2) { // Length-delimited
                    let length = 0;
                    shift = 0;
                    do {
                        if (offset >= payload.length) break;
                        byte = payload[offset++] & 0xFF;
                        length |= (byte & 0x7F) << shift;
                        shift += 7;
                    } while ((byte & 0x80) !== 0);
                    
                    if (offset + length <= payload.length) {
                        const fieldData = Java.array('byte', Array.prototype.slice.call(payload, offset, offset + length));
                        const fieldStr = bytesToString(fieldData);
                        
                        console.log("        Length: " + length + " bytes");
                        
                        // Check if this field contains FCM token
                        if (fieldNumber === 13 || fieldStr.includes("APA91b")) { // Field 13 is pn_token
                            console.log("        FCM TOKEN FIELD: " + fieldStr);
                        } else if (fieldStr.includes("android_")) {
                            console.log("        MACHINE CODE: " + fieldStr);
                        } else if (fieldStr.length > 50) {
                            console.log("        Data: " + fieldStr.substring(0, 50) + "...");
                        } else {
                            console.log("        Data: " + fieldStr);
                        }
                        
                        offset += length;
                    } else {
                        break;
                    }
                } else {
                    // Skip unknown wire types
                    break;
                }
                
                fieldCount++;
            } catch (e) {
                console.log("      Analysis error at offset " + offset + ": " + e.message);
                break;
            }
        }
        
        console.log("    === End Analysis ===");
    }
    
    // Hook TCP packet creation for detailed analysis
    try {
        const TcpPacket = Java.use("com.beetalklib.network.tcp.f");
        
        TcpPacket.$init.overload('int', '[B').implementation = function(command, payload) {
            if (command === 0x43) {
                console.log("\n" + "=".repeat(80));
                console.log("🔍 DETAILED SetUserInfo TCP PACKET ANALYSIS");
                console.log("=".repeat(80));
                
                console.log("📦 Packet Info:");
                console.log("    Command: 0x" + command.toString(16) + " (" + command + ")");
                console.log("    Payload size: " + payload.length + " bytes");
                
                if (payload && payload.length > 0) {
                    // Extract FCM token
                    const fcmToken = findFCMToken(payload);
                    if (fcmToken) {
                        console.log("🎯 FCM Token Found: " + fcmToken);
                        console.log("    Token length: " + fcmToken.length);
                        console.log("    Token prefix: " + fcmToken.split(':')[0]);
                    }
                    
                    // Extract machine code
                    const machineCode = findMachineCode(payload);
                    if (machineCode) {
                        console.log("🔧 Machine Code: " + machineCode);
                    }
                    
                    // Show hex dump of first 100 bytes
                    console.log("📋 Hex Dump (first 100 bytes):");
                    const dumpSize = Math.min(payload.length, 100);
                    const hexDump = bytesToHex(Java.array('byte', Array.prototype.slice.call(payload, 0, dumpSize)));
                    console.log("    " + hexDump);
                    
                    // Try to analyze as protocol buffer
                    try {
                        analyzeProtocolBuffer(payload);
                    } catch (e) {
                        console.log("    Protocol buffer analysis failed: " + e.message);
                    }
                    
                    // String analysis
                    console.log("📝 String Analysis:");
                    try {
                        const payloadStr = bytesToString(payload);
                        const printableChars = payloadStr.replace(/[^\x20-\x7E]/g, '.');
                        console.log("    Printable content: " + printableChars.substring(0, 200) + "...");
                        
                        // Look for specific patterns
                        if (payloadStr.includes("Brand/")) {
                            const brandMatch = payloadStr.match(/Brand\/[^\\s]+\\s+Model\/[^\\s]+/);
                            if (brandMatch) {
                                console.log("    Device info: " + brandMatch[0]);
                            }
                        }
                        
                        if (payloadStr.includes("OSVer/")) {
                            const osMatch = payloadStr.match(/OSVer\/\\d+/);
                            if (osMatch) {
                                console.log("    OS Version: " + osMatch[0]);
                            }
                        }
                        
                    } catch (e) {
                        console.log("    String analysis failed: " + e.message);
                    }
                }
                
                console.log("=".repeat(80) + "\n");
            }
            
            return this.$init(command, payload);
        };
        
        console.log("[+] TCP Payload Analyzer hooks installed");
    } catch (e) {
        console.log("[-] Failed to install payload analyzer: " + e.message);
    }
    
    // Also hook the network send to correlate with request info
    try {
        const NetworkManager = Java.use("com.shopee.app.network.e");
        
        NetworkManager.p.implementation = function(tcpPacket, requestId, userId, wireMessage) {
            if (tcpPacket && tcpPacket.b() === 0x43) {
                console.log("🌐 Network Context:");
                console.log("    Request ID: " + requestId);
                console.log("    User ID: " + userId);
                console.log("    Wire Message: " + (wireMessage ? wireMessage.getClass().getName() : "null"));
            }
            
            return this.p(tcpPacket, requestId, userId, wireMessage);
        };
        
        console.log("[+] Network context hooks installed");
    } catch (e) {
        console.log("[-] Failed to install network hooks: " + e.message);
    }
    
    console.log("[+] TCP Payload Analyzer ready!");
});
