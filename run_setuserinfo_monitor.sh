#!/bin/bash

# SetUserInfo FCM Token Monitor - Quick Start Script
# Author: Security Researcher
# Description: Easy deployment script for monitoring SetUserInfo usage in Shopee Partner app

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
APP_PACKAGE="com.shopee.vn.partner"
SCRIPT_NAME="setuserinfo_logger.js"
LOG_DIR="./logs"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
LOG_FILE="${LOG_DIR}/setuserinfo_${TIMESTAMP}.log"

# Functions
print_banner() {
    echo -e "${PURPLE}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                SetUserInfo FCM Token Monitor                 ║"
    echo "║                     Frida Security Tool                      ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

print_info() {
    echo -e "${CYAN}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

check_dependencies() {
    print_info "Checking dependencies..."
    
    # Check if frida is installed
    if ! command -v frida &> /dev/null; then
        print_error "Frida is not installed. Please install it first:"
        echo "  pip install frida-tools"
        exit 1
    fi
    
    # Check if frida script exists
    if [ ! -f "$SCRIPT_NAME" ]; then
        print_error "Frida script '$SCRIPT_NAME' not found in current directory"
        exit 1
    fi
    
    # Check if device is connected
    if ! frida-ls-devices | grep -q "usb"; then
        print_warning "No USB device detected. Make sure device is connected and USB debugging is enabled."
    fi
    
    print_success "Dependencies check completed"
}

setup_logging() {
    print_info "Setting up logging..."
    
    # Create log directory
    mkdir -p "$LOG_DIR"
    
    print_success "Log directory created: $LOG_DIR"
    print_info "Log file: $LOG_FILE"
}

list_devices() {
    print_info "Available devices:"
    frida-ls-devices
}

check_app() {
    print_info "Checking if app is installed..."
    
    # Try to find the app
    if frida-ps -U | grep -q "$APP_PACKAGE"; then
        print_success "App is running: $APP_PACKAGE"
        return 0
    elif frida-ps -Ua | grep -q "$APP_PACKAGE"; then
        print_info "App is installed but not running: $APP_PACKAGE"
        return 1
    else
        print_error "App not found: $APP_PACKAGE"
        print_info "Please install the Shopee Partner VN app first"
        exit 1
    fi
}

start_monitoring() {
    local mode=$1
    local device_id=$2
    
    print_info "Starting SetUserInfo monitoring..."
    print_info "Mode: $mode"
    print_info "Target: $APP_PACKAGE"
    print_info "Script: $SCRIPT_NAME"
    print_info "Log: $LOG_FILE"
    
    echo -e "${YELLOW}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                     MONITORING ACTIVE                       ║"
    echo "║                                                              ║"
    echo "║  • FCM token registration will be logged                    ║"
    echo "║  • SetUserInfo protocol usage will be tracked              ║"
    echo "║  • Login flows will be monitored                           ║"
    echo "║                                                              ║"
    echo "║  Press Ctrl+C to stop monitoring                           ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
    
    # Build frida command
    local frida_cmd="frida"
    
    if [ "$device_id" != "" ]; then
        frida_cmd="$frida_cmd -D $device_id"
    else
        frida_cmd="$frida_cmd -U"
    fi
    
    if [ "$mode" == "spawn" ]; then
        frida_cmd="$frida_cmd -f $APP_PACKAGE --no-pause"
    else
        frida_cmd="$frida_cmd $APP_PACKAGE"
    fi
    
    frida_cmd="$frida_cmd -l $SCRIPT_NAME"
    
    # Execute frida command with logging
    echo -e "${CYAN}[CMD]${NC} $frida_cmd"
    
    # Run with tee to both display and log
    $frida_cmd 2>&1 | tee "$LOG_FILE"
}

analyze_logs() {
    print_info "Available log files:"
    ls -la "$LOG_DIR"/*.log 2>/dev/null || print_warning "No log files found"
    
    # Check if analyzer script exists
    if [ -f "analyze_setuserinfo_logs.py" ]; then
        echo
        print_info "To analyze logs, run:"
        echo "  python3 analyze_setuserinfo_logs.py $LOG_FILE"
        echo "  python3 analyze_setuserinfo_logs.py $LOG_FILE --json results.json"
    else
        print_warning "Log analyzer script not found"
    fi
}

show_help() {
    echo "Usage: $0 [OPTIONS]"
    echo
    echo "Options:"
    echo "  -h, --help          Show this help message"
    echo "  -l, --list          List available devices"
    echo "  -s, --spawn         Spawn app instead of attaching"
    echo "  -d, --device ID     Use specific device ID"
    echo "  -a, --analyze       Show log analysis options"
    echo "  --check             Check dependencies and app status"
    echo
    echo "Examples:"
    echo "  $0                  # Attach to running app on USB device"
    echo "  $0 -s               # Spawn app and start monitoring"
    echo "  $0 -d emulator-5554 # Use specific device"
    echo "  $0 -l               # List available devices"
    echo "  $0 -a               # Show log analysis options"
}

# Main script
main() {
    print_banner
    
    # Parse command line arguments
    local mode="attach"
    local device_id=""
    local show_devices=false
    local show_analysis=false
    local check_only=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -l|--list)
                show_devices=true
                shift
                ;;
            -s|--spawn)
                mode="spawn"
                shift
                ;;
            -d|--device)
                device_id="$2"
                shift 2
                ;;
            -a|--analyze)
                show_analysis=true
                shift
                ;;
            --check)
                check_only=true
                shift
                ;;
            *)
                print_error "Unknown option: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # Execute based on options
    if [ "$show_devices" = true ]; then
        list_devices
        exit 0
    fi
    
    if [ "$show_analysis" = true ]; then
        analyze_logs
        exit 0
    fi
    
    # Check dependencies
    check_dependencies
    
    if [ "$check_only" = true ]; then
        check_app
        print_success "All checks passed"
        exit 0
    fi
    
    # Setup logging
    setup_logging
    
    # Check app status
    if ! check_app && [ "$mode" == "attach" ]; then
        print_warning "App is not running. Use -s to spawn the app."
        read -p "Do you want to spawn the app? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            mode="spawn"
        else
            exit 1
        fi
    fi
    
    # Start monitoring
    start_monitoring "$mode" "$device_id"
    
    # Show analysis options after monitoring
    echo
    print_success "Monitoring completed"
    analyze_logs
}

# Trap Ctrl+C
trap 'echo -e "\n${YELLOW}[INFO]${NC} Monitoring stopped by user"; exit 0' INT

# Run main function
main "$@"
