/**
 * <PERSON><PERSON> to Log SetUserInfo Usage for FCM Token Registration
 * Monitors all SetUserInfo protocol buffer creation and network requests
 * Author: Security Researcher
 * Target: Shopee Partner VN App
 */

Java.perform(function () {
    console.log("[+] Starting SetUserInfo FCM Token Logger...");

    // Color codes for better output
    const colors = {
        reset: '\x1b[0m',
        bright: '\x1b[1m',
        red: '\x1b[31m',
        green: '\x1b[32m',
        yellow: '\x1b[33m',
        blue: '\x1b[34m',
        magenta: '\x1b[35m',
        cyan: '\x1b[36m'
    };

    // Utility function to format logs
    function logInfo(tag, message) {
        console.log(colors.cyan + "[INFO]" + colors.reset + " " + colors.bright + tag + colors.reset + ": " + message);
    }

    function logSuccess(tag, message) {
        console.log(colors.green + "[SUCCESS]" + colors.reset + " " + colors.bright + tag + colors.reset + ": " + message);
    }

    function logWarning(tag, message) {
        console.log(colors.yellow + "[WARNING]" + colors.reset + " " + colors.bright + tag + colors.reset + ": " + message);
    }

    function logError(tag, message) {
        console.log(colors.red + "[ERROR]" + colors.reset + " " + colors.bright + tag + colors.reset + ": " + message);
    }

    // Helper function to safely get string value
    function safeGetString(obj) {
        try {
            if (obj === null || obj === undefined) return "null";
            return obj.toString();
        } catch (e) {
            return "Error getting string: " + e.message;
        }
    }

    // Helper function to get ByteString content
    function getByteStringContent(byteString) {
        try {
            if (byteString === null || byteString === undefined) return "null";
            return byteString.utf8();
        } catch (e) {
            try {
                return byteString.toString();
            } catch (e2) {
                return "Error reading ByteString: " + e2.message;
            }
        }
    }

    // 1. Hook SetUserInfo Builder Creation
    try {
        const SetUserInfoBuilder = Java.use("com.shopee.protocol.action.SetUserInfo$Builder");

        SetUserInfoBuilder.$init.implementation = function () {
            logInfo("SetUserInfo.Builder", "New SetUserInfo Builder created");
            console.log("    Stack trace:");
            Java.perform(function () {
                const Exception = Java.use("java.lang.Exception");
                const stackTrace = Exception.$new().getStackTrace();
                for (let i = 0; i < Math.min(stackTrace.length, 10); i++) {
                    console.log("      " + stackTrace[i].toString());
                }
            });
            return this.$init();
        };

        // Hook pn_token setting
        SetUserInfoBuilder.pn_token.implementation = function (token) {
            const tokenContent = getByteStringContent(token);
            logSuccess("FCM_TOKEN", "Setting FCM token: " + tokenContent);
            console.log("    Token length: " + (token ? token.size() : 0));
            return this.pn_token(token);
        };

        // Hook machine_code setting
        SetUserInfoBuilder.machine_code.implementation = function (machineCode) {
            logSuccess("MACHINE_CODE", "Setting machine code: " + safeGetString(machineCode));
            return this.machine_code(machineCode);
        };

        // Hook requestid setting
        SetUserInfoBuilder.requestid.implementation = function (requestId) {
            logInfo("REQUEST_ID", "Setting request ID: " + safeGetString(requestId));
            return this.requestid(requestId);
        };

        // Hook build method
        SetUserInfoBuilder.build.implementation = function () {
            logSuccess("BUILD", "Building SetUserInfo protocol buffer");
            const result = this.build();

            // Try to extract field values
            try {
                if (this.pn_token.value) {
                    console.log("    FCM Token: " + getByteStringContent(this.pn_token.value));
                }
                if (this.machine_code.value) {
                    console.log("    Machine Code: " + this.machine_code.value);
                }
                if (this.requestid.value) {
                    console.log("    Request ID: " + this.requestid.value);
                }
            } catch (e) {
                logWarning("BUILD", "Could not extract field values: " + e.message);
            }

            return result;
        };

        logSuccess("HOOK", "SetUserInfo Builder hooks installed");
    } catch (e) {
        logError("HOOK", "Failed to hook SetUserInfo Builder: " + e.message);
    }

    // 2. Hook SetUserInfo Request Classes
    const requestClasses = ["z", "x", "y", "c0"];

    requestClasses.forEach(className => {
        try {
            const RequestClass = Java.use("com.shopee.app.network.request." + className);

            // Hook constructor
            RequestClass.$init.implementation = function () {
                logInfo("REQUEST_" + className.toUpperCase(), "Creating SetUserInfo request class " + className);
                return this.$init();
            };

            // Hook the h method (parameter setting)
            if (RequestClass.h) {
                RequestClass.h.overloads.forEach(overload => {
                    overload.implementation = function () {
                        logSuccess("PARAMS_" + className.toUpperCase(), "Setting parameters for request class " + className);
                        console.log("    Parameters count: " + arguments.length);

                        // Log parameters
                        for (let i = 0; i < arguments.length; i++) {
                            const param = arguments[i];
                            if (param !== null && param !== undefined) {
                                if (typeof param === 'string') {
                                    console.log("    Param[" + i + "]: " + param);
                                } else if (param.constructor && param.constructor.name === '[B') {
                                    console.log("    Param[" + i + "]: byte array length " + param.length);
                                } else {
                                    console.log("    Param[" + i + "]: " + param.toString());
                                }
                            }
                        }

                        return this.h.apply(this, arguments);
                    };
                });
            }

            // Hook the d method (TCP packet creation)
            if (RequestClass.d) {
                RequestClass.d.implementation = function () {
                    logSuccess("TCP_PACKET_" + className.toUpperCase(), "Creating TCP packet for SetUserInfo");
                    const result = this.d();

                    if (result) {
                        console.log("    Command: 0x" + result.b().toString(16) + " (should be 0x43)");
                        console.log("    Payload size: " + (result.c() ? result.c().length : 0) + " bytes");
                    }

                    return result;
                };
            }

            logSuccess("HOOK", "Request class " + className + " hooks installed");
        } catch (e) {
            logWarning("HOOK", "Could not hook request class " + className + ": " + e.message);
        }
    });

    // 3. Hook Network Manager TCP Send
    try {
        const NetworkManager = Java.use("com.shopee.app.network.e");

        NetworkManager.p.implementation = function (tcpPacket, requestId, userId, wireMessage) {
            if (tcpPacket && tcpPacket.b() === 0x43) {
                logSuccess("NETWORK", "Sending SetUserInfo TCP packet (0x43)");
                console.log("    Request ID: " + safeGetString(requestId));
                console.log("    User ID: " + safeGetString(userId));
                console.log("    Packet size: " + (tcpPacket.c() ? tcpPacket.c().length : 0) + " bytes");

                // Try to log the wire message details
                if (wireMessage) {
                    console.log("    Wire message type: " + wireMessage.getClass().getName());
                }
            }

            return this.p(tcpPacket, requestId, userId, wireMessage);
        };

        logSuccess("HOOK", "Network Manager hooks installed");
    } catch (e) {
        logError("HOOK", "Failed to hook Network Manager: " + e.message);
    }

    // 4. Hook FCM Token Registration Entry Point
    try {
        const NotificationUtil = Java.use("com.shopee.app.pushnotification.g");

        NotificationUtil.e.implementation = function (pushToken, userInfo, deviceStore, machineCode) {
            logSuccess("FCM_REGISTER", "FCM Token Registration Called");
            console.log("    Push Token: " + safeGetString(pushToken));
            console.log("    Machine Code: " + safeGetString(machineCode));

            if (userInfo) {
                try {
                    console.log("    User logged in: " + userInfo.isLoggedIn());
                    if (userInfo.isLoggedIn()) {
                        console.log("    User ID: " + userInfo.getUserId());
                    }
                } catch (e) {
                    logWarning("FCM_REGISTER", "Could not get user info: " + e.message);
                }
            }

            return this.e(pushToken, userInfo, deviceStore, machineCode);
        };

        logSuccess("HOOK", "FCM Registration hooks installed");
    } catch (e) {
        logError("HOOK", "Failed to hook FCM Registration: " + e.message);
    }

    // 5. Hook Login Processors
    try {
        const LoginProcessor = Java.use("com.shopee.app.network.processors.login.e$a");

        LoginProcessor.a.implementation = function (responseCommon, loginMethod) {
            logSuccess("LOGIN", "Login processor called");
            console.log("    Login method: " + loginMethod);

            if (responseCommon) {
                try {
                    console.log("    Request ID: " + safeGetString(responseCommon.requestid.value));
                    console.log("    Error code: " + (responseCommon.errcode.value ? responseCommon.errcode.value : "0"));
                } catch (e) {
                    logWarning("LOGIN", "Could not extract response details: " + e.message);
                }
            }

            return this.a(responseCommon, loginMethod);
        };

        logSuccess("HOOK", "Login Processor hooks installed");
    } catch (e) {
        logWarning("HOOK", "Could not hook Login Processor: " + e.message);
    }

    // 6. Hook FCM Service Token Update
    try {
        const FcmService = Java.use("com.shopee.app.pushnotification.fcm.ShopeeFcmMessageService");

        FcmService.onNewToken.implementation = function (token) {
            logSuccess("FCM_NEW_TOKEN", "New FCM token received");
            console.log("    Token: " + safeGetString(token));
            console.log("    Token length: " + (token ? token.length : 0));

            return this.onNewToken(token);
        };

        logSuccess("HOOK", "FCM Service hooks installed");
    } catch (e) {
        logWarning("HOOK", "Could not hook FCM Service: " + e.message);
    }

    // 7. Hook SetUserInfo Response Processor
    try {
        const SetUserInfoProcessor = Java.use("com.shopee.app.network.processors.l0");

        SetUserInfoProcessor.i.implementation = function (responseBytes, length) {
            logSuccess("RESPONSE", "SetUserInfo response received");
            console.log("    Response length: " + length + " bytes");

            const result = this.i(responseBytes, length);

            // Try to parse response
            try {
                const Wire = Java.use("com.squareup.wire.Wire");
                const ResponseCommon = Java.use("com.shopee.protocol.action.ResponseCommon");
                const wire = Wire.a.value;
                const response = wire.parseFrom(responseBytes, 0, length, ResponseCommon.class);

                if (response) {
                    console.log("    Request ID: " + safeGetString(response.requestid.value));
                    console.log("    Error code: " + (response.errcode.value ? response.errcode.value : "0"));
                    console.log("    Success: " + (response.errcode.value === 0));
                }
            } catch (e) {
                logWarning("RESPONSE", "Could not parse response: " + e.message);
            }

            return result;
        };

        logSuccess("HOOK", "SetUserInfo Response Processor hooks installed");
    } catch (e) {
        logWarning("HOOK", "Could not hook SetUserInfo Response Processor: " + e.message);
    }

    // 8. Hook User Login State Changes
    try {
        const UserInfo = Java.use("com.shopee.app.appuser.UserInfo");

        UserInfo.setToken.implementation = function (token) {
            logSuccess("USER_AUTH", "User authentication token set");
            console.log("    Token: " + safeGetString(token));

            return this.setToken(token);
        };

        UserInfo.setUserId.implementation = function (userId) {
            logSuccess("USER_ID", "User ID set: " + userId);

            return this.setUserId(userId);
        };

        logSuccess("HOOK", "UserInfo hooks installed");
    } catch (e) {
        logWarning("HOOK", "Could not hook UserInfo: " + e.message);
    }

    // 9. Hook TCP Packet Creation
    try {
        const TcpPacket = Java.use("com.beetalklib.network.tcp.f");

        TcpPacket.$init.overload('int', '[B').implementation = function (command, payload) {
            if (command === 0x43) {
                logSuccess("TCP_PACKET", "Creating SetUserInfo TCP packet");
                console.log("    Command: 0x" + command.toString(16));
                console.log("    Payload size: " + (payload ? payload.length : 0) + " bytes");

                // Try to decode the payload as SetUserInfo
                try {
                    const Wire = Java.use("com.squareup.wire.Wire");
                    const SetUserInfo = Java.use("com.shopee.protocol.action.SetUserInfo");
                    const wire = Wire.a.value;
                    const setUserInfo = wire.parseFrom(payload, 0, payload.length, SetUserInfo.class);

                    if (setUserInfo) {
                        console.log("    Decoded SetUserInfo:");
                        if (setUserInfo.pn_token.value) {
                            console.log("      FCM Token: " + getByteStringContent(setUserInfo.pn_token.value));
                        }
                        if (setUserInfo.machine_code.value) {
                            console.log("      Machine Code: " + setUserInfo.machine_code.value);
                        }
                        if (setUserInfo.requestid.value) {
                            console.log("      Request ID: " + setUserInfo.requestid.value);
                        }
                    }
                } catch (e) {
                    logWarning("TCP_PACKET", "Could not decode SetUserInfo payload: " + e.message);
                }
            }

            return this.$init(command, payload);
        };

        logSuccess("HOOK", "TCP Packet hooks installed");
    } catch (e) {
        logWarning("HOOK", "Could not hook TCP Packet: " + e.message);
    }

    // 10. Hook Login Success Callbacks
    try {
        const ToCLoginProcessor = Java.use("com.shopee.app.web.processor.WebDidFinishToCLoginProcessor");

        ToCLoginProcessor.sendUserData.implementation = function () {
            logSuccess("LOGIN_SUCCESS", "ToC Login success - sending user data");
            console.log("    This will trigger FCM token registration");

            return this.sendUserData();
        };

        logSuccess("HOOK", "ToC Login Processor hooks installed");
    } catch (e) {
        logWarning("HOOK", "Could not hook ToC Login Processor: " + e.message);
    }

    // 11. Monitor Device Store FCM Token Storage
    try {
        const DeviceStore = Java.use("com.shopee.app.data.store.j0");

        DeviceStore.N.implementation = function (token) {
            logSuccess("STORAGE", "Storing FCM token in device store");
            console.log("    Token: " + safeGetString(token));

            return this.N(token);
        };

        DeviceStore.M.implementation = function (machineCode) {
            logInfo("STORAGE", "Storing machine code: " + safeGetString(machineCode));

            return this.M(machineCode);
        };

        logSuccess("HOOK", "Device Store hooks installed");
    } catch (e) {
        logWarning("HOOK", "Could not hook Device Store: " + e.message);
    }

    // 12. Summary function to print current state
    function printCurrentState() {
        console.log(colors.magenta + "\n=== CURRENT STATE SUMMARY ===" + colors.reset);

        try {
            const app = Java.use("com.shopee.app.application.ShopeeApplication").get();
            const userComponent = app.getComponent();
            const userInfo = userComponent.loggedInUser();

            console.log("User logged in: " + userInfo.isLoggedIn());
            if (userInfo.isLoggedIn()) {
                console.log("User ID: " + userInfo.getUserId());
            }

            const deviceStore = userComponent.deviceStore();
            console.log("Stored FCM token: " + safeGetString(deviceStore.j()));
            console.log("Stored machine code: " + safeGetString(deviceStore.i()));

        } catch (e) {
            logWarning("STATE", "Could not get current state: " + e.message);
        }

        console.log(colors.magenta + "==============================\n" + colors.reset);
    }

    // Print initial state
    setTimeout(printCurrentState, 2000);

    // Set up periodic state monitoring
    setInterval(function () {
        // Only print if there's activity
        if (Math.random() < 0.1) { // 10% chance every interval
            printCurrentState();
        }
    }, 30000); // Every 30 seconds

    console.log(colors.green + "[+] SetUserInfo FCM Token Logger initialized successfully!" + colors.reset);
    console.log(colors.yellow + "[*] Monitoring FCM token registration and SetUserInfo protocol usage..." + colors.reset);
    console.log(colors.cyan + "[*] Use 'printCurrentState()' in console to see current state" + colors.reset);
});
