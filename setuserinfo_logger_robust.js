/**
 * Robust SetUserInfo FCM Token Logger - Frida <PERSON>
 * Focuses on reliable data extraction without complex protocol buffer parsing
 * Author: Security Researcher
 * Target: Shopee Partner VN App
 */

Java.perform(function() {
    console.log("[+] Starting Robust SetUserInfo FCM Token Logger...");
    
    // Color codes for better output
    const colors = {
        reset: '\x1b[0m',
        bright: '\x1b[1m',
        red: '\x1b[31m',
        green: '\x1b[32m',
        yellow: '\x1b[33m',
        blue: '\x1b[34m',
        magenta: '\x1b[35m',
        cyan: '\x1b[36m'
    };

    // Utility functions
    function logInfo(tag, message) {
        console.log(colors.cyan + "[INFO]" + colors.reset + " " + colors.bright + tag + colors.reset + ": " + message);
    }

    function logSuccess(tag, message) {
        console.log(colors.green + "[SUCCESS]" + colors.reset + " " + colors.bright + tag + colors.reset + ": " + message);
    }

    function logWarning(tag, message) {
        console.log(colors.yellow + "[WARNING]" + colors.reset + " " + colors.bright + tag + colors.reset + ": " + message);
    }

    function logError(tag, message) {
        console.log(colors.red + "[ERROR]" + colors.reset + " " + colors.bright + tag + colors.reset + ": " + message);
    }

    function safeGetString(obj) {
        try {
            if (obj === null || obj === undefined) return "null";
            return obj.toString();
        } catch (e) {
            return "Error getting string: " + e.message;
        }
    }

    function getByteStringContent(byteString) {
        try {
            if (byteString === null || byteString === undefined) return "null";
            return byteString.utf8();
        } catch (e) {
            try {
                return byteString.toString();
            } catch (e2) {
                return "Error reading ByteString: " + e2.message;
            }
        }
    }

    function analyzeRawPayload(payload) {
        try {
            // Convert payload to string for pattern analysis
            const payloadStr = Java.use("java.lang.String").$new(payload, "UTF-8");
            const analysis = {
                size: payload.length,
                containsFCM: false,
                containsGCM: false,
                containsHMS: false,
                containsGPNS: false,
                fcmTokenPattern: null
            };
            
            // Look for FCM token patterns
            if (payloadStr.contains("APA91b")) {
                analysis.containsFCM = true;
                // Try to extract FCM token
                const tokenRegex = /[A-Za-z0-9_-]{20,}:APA91b[A-Za-z0-9_-]{100,}/g;
                const matches = payloadStr.match(tokenRegex);
                if (matches && matches.length > 0) {
                    analysis.fcmTokenPattern = matches[0];
                }
            }
            
            // Look for machine codes
            if (payloadStr.contains("android_gcm")) analysis.containsGCM = true;
            if (payloadStr.contains("android_hms")) analysis.containsHMS = true;
            if (payloadStr.contains("android_gpns")) analysis.containsGPNS = true;
            
            return analysis;
        } catch (e) {
            return { size: payload.length, error: e.message };
        }
    }

    // 1. Hook SetUserInfo Builder - Most Reliable Source
    try {
        const SetUserInfoBuilder = Java.use("com.shopee.protocol.action.SetUserInfo$Builder");
        
        // Hook constructors safely
        if (SetUserInfoBuilder.$init.overloads) {
            SetUserInfoBuilder.$init.overloads.forEach((overload, index) => {
                overload.implementation = function() {
                    logInfo("BUILDER", `SetUserInfo Builder created (constructor ${index})`);
                    return this.$init.apply(this, arguments);
                };
            });
        }

        // Hook pn_token - This is our primary FCM token source
        try {
            if (SetUserInfoBuilder.pn_token.overloads) {
                SetUserInfoBuilder.pn_token.overloads.forEach(overload => {
                    overload.implementation = function(token) {
                        const tokenContent = getByteStringContent(token);
                        logSuccess("FCM_TOKEN", "FCM token set in builder");
                        console.log("    Token: " + tokenContent);
                        console.log("    Token length: " + (token ? token.size() : 0));
                        console.log("    Token type: " + (token ? token.getClass().getName() : "null"));
                        return this.pn_token(token);
                    };
                });
            } else {
                SetUserInfoBuilder.pn_token.implementation = function(token) {
                    const tokenContent = getByteStringContent(token);
                    logSuccess("FCM_TOKEN", "FCM token set in builder");
                    console.log("    Token: " + tokenContent);
                    console.log("    Token length: " + (token ? token.size() : 0));
                    return this.pn_token(token);
                };
            }
        } catch (e) {
            logWarning("HOOK", "Could not hook pn_token: " + e.message);
        }

        // Hook machine_code
        try {
            SetUserInfoBuilder.machine_code.implementation = function(machineCode) {
                logSuccess("MACHINE_CODE", "Machine code set: " + safeGetString(machineCode));
                return this.machine_code(machineCode);
            };
        } catch (e) {
            logWarning("HOOK", "Could not hook machine_code: " + e.message);
        }

        // Hook requestid
        try {
            SetUserInfoBuilder.requestid.implementation = function(requestId) {
                logInfo("REQUEST_ID", "Request ID set: " + safeGetString(requestId));
                return this.requestid(requestId);
            };
        } catch (e) {
            logWarning("HOOK", "Could not hook requestid: " + e.message);
        }

        // Hook build method
        try {
            SetUserInfoBuilder.build.implementation = function() {
                logSuccess("BUILD", "Building SetUserInfo protocol buffer");
                const result = this.build();
                console.log("    Built SetUserInfo object: " + result.getClass().getName());
                return result;
            };
        } catch (e) {
            logWarning("HOOK", "Could not hook build: " + e.message);
        }

        logSuccess("HOOK", "SetUserInfo Builder hooks installed");
    } catch (e) {
        logError("HOOK", "Failed to hook SetUserInfo Builder: " + e.message);
    }

    // 2. Hook TCP Packet Creation - Raw Payload Analysis
    try {
        const TcpPacket = Java.use("com.beetalklib.network.tcp.f");
        
        TcpPacket.$init.overload('int', '[B').implementation = function(command, payload) {
            if (command === 0x43) {
                logSuccess("TCP_PACKET", "Creating SetUserInfo TCP packet");
                console.log("    Command: 0x" + command.toString(16));
                console.log("    Payload size: " + (payload ? payload.length : 0) + " bytes");
                
                // Analyze raw payload
                if (payload && payload.length > 0) {
                    const analysis = analyzeRawPayload(payload);
                    console.log("    Payload analysis:");
                    console.log("      Size: " + analysis.size + " bytes");
                    console.log("      Contains FCM pattern: " + analysis.containsFCM);
                    console.log("      Contains android_gcm: " + analysis.containsGCM);
                    console.log("      Contains android_hms: " + analysis.containsHMS);
                    console.log("      Contains android_gpns: " + analysis.containsGPNS);
                    
                    if (analysis.fcmTokenPattern) {
                        console.log("      Extracted FCM token: " + analysis.fcmTokenPattern);
                    }
                    
                    if (analysis.error) {
                        console.log("      Analysis error: " + analysis.error);
                    }
                }
            }
            
            return this.$init(command, payload);
        };

        logSuccess("HOOK", "TCP Packet hooks installed");
    } catch (e) {
        logWarning("HOOK", "Could not hook TCP Packet: " + e.message);
    }

    // 3. Hook Network Manager - Request Tracking
    try {
        const NetworkManager = Java.use("com.shopee.app.network.e");
        
        NetworkManager.p.implementation = function(tcpPacket, requestId, userId, wireMessage) {
            if (tcpPacket && tcpPacket.b() === 0x43) {
                logSuccess("NETWORK", "Sending SetUserInfo TCP packet (0x43)");
                console.log("    Request ID: " + safeGetString(requestId));
                console.log("    User ID: " + safeGetString(userId));
                console.log("    Packet size: " + (tcpPacket.c() ? tcpPacket.c().length : 0) + " bytes");
                console.log("    Wire message type: " + (wireMessage ? wireMessage.getClass().getName() : "null"));
            }
            
            return this.p(tcpPacket, requestId, userId, wireMessage);
        };

        logSuccess("HOOK", "Network Manager hooks installed");
    } catch (e) {
        logError("HOOK", "Failed to hook Network Manager: " + e.message);
    }

    // 4. Hook FCM Registration Entry Point
    try {
        const NotificationUtil = Java.use("com.shopee.app.pushnotification.g");
        
        NotificationUtil.e.implementation = function(pushToken, userInfo, deviceStore, machineCode) {
            logSuccess("FCM_REGISTER", "FCM Token Registration Called");
            console.log("    Push Token: " + safeGetString(pushToken));
            console.log("    Machine Code: " + safeGetString(machineCode));
            
            if (userInfo) {
                try {
                    console.log("    User logged in: " + userInfo.isLoggedIn());
                    if (userInfo.isLoggedIn()) {
                        console.log("    User ID: " + userInfo.getUserId());
                    }
                } catch (e) {
                    logWarning("FCM_REGISTER", "Could not get user info: " + e.message);
                }
            }
            
            return this.e(pushToken, userInfo, deviceStore, machineCode);
        };

        logSuccess("HOOK", "FCM Registration hooks installed");
    } catch (e) {
        logError("HOOK", "Failed to hook FCM Registration: " + e.message);
    }

    // 5. Hook Request Class Parameter Setting
    const requestClasses = ["z", "x", "y"];
    
    requestClasses.forEach(className => {
        try {
            const RequestClass = Java.use("com.shopee.app.network.request." + className);
            
            // Hook parameter setting method
            if (RequestClass.h && RequestClass.h.overloads) {
                RequestClass.h.overloads.forEach(overload => {
                    overload.implementation = function() {
                        logSuccess("PARAMS", `Setting parameters for request class ${className}`);
                        console.log("    Parameters count: " + arguments.length);
                        
                        // Log parameters with FCM token detection
                        for (let i = 0; i < arguments.length; i++) {
                            const param = arguments[i];
                            if (param !== null && param !== undefined) {
                                const paramStr = safeGetString(param);
                                if (paramStr.includes("APA91b")) {
                                    console.log("    Param[" + i + "]: FCM TOKEN - " + paramStr);
                                } else if (paramStr.includes("android_")) {
                                    console.log("    Param[" + i + "]: MACHINE CODE - " + paramStr);
                                } else if (paramStr.length > 100) {
                                    console.log("    Param[" + i + "]: LONG DATA - " + paramStr.substring(0, 50) + "...");
                                } else {
                                    console.log("    Param[" + i + "]: " + paramStr);
                                }
                            }
                        }
                        
                        return this.h.apply(this, arguments);
                    };
                });
            }

            logSuccess("HOOK", `Request class ${className} hooks installed`);
        } catch (e) {
            logWarning("HOOK", `Could not hook request class ${className}: ${e.message}`);
        }
    });

    // 6. Hook Response Processing
    try {
        const SetUserInfoProcessor = Java.use("com.shopee.app.network.processors.l0");
        
        SetUserInfoProcessor.i.implementation = function(responseBytes, length) {
            logSuccess("RESPONSE", "SetUserInfo response received");
            console.log("    Response length: " + length + " bytes");
            
            // Simple response analysis
            try {
                const responseStr = Java.use("java.lang.String").$new(responseBytes, 0, length, "UTF-8");
                console.log("    Response contains success indicators: " + responseStr.contains("success"));
            } catch (e) {
                console.log("    Response analysis failed: " + e.message);
            }
            
            return this.i(responseBytes, length);
        };

        logSuccess("HOOK", "Response Processor hooks installed");
    } catch (e) {
        logWarning("HOOK", "Could not hook Response Processor: " + e.message);
    }

    console.log(colors.green + "[+] Robust SetUserInfo FCM Token Logger initialized!" + colors.reset);
    console.log(colors.yellow + "[*] Focusing on reliable data extraction without complex parsing..." + colors.reset);
});
