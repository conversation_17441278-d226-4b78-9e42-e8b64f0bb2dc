/**
 * Simplified SetUserInfo FCM Token Logger - Fr<PERSON> Script
 * Focuses on core functionality with robust error handling
 * Author: Security Researcher
 * Target: Shopee Partner VN App
 */

Java.perform(function() {
    console.log("[+] Starting Simplified SetUserInfo FCM Token Logger...");
    
    // Color codes for better output
    const colors = {
        reset: '\x1b[0m',
        bright: '\x1b[1m',
        red: '\x1b[31m',
        green: '\x1b[32m',
        yellow: '\x1b[33m',
        blue: '\x1b[34m',
        magenta: '\x1b[35m',
        cyan: '\x1b[36m'
    };

    // Utility functions
    function logInfo(tag, message) {
        console.log(colors.cyan + "[INFO]" + colors.reset + " " + colors.bright + tag + colors.reset + ": " + message);
    }

    function logSuccess(tag, message) {
        console.log(colors.green + "[SUCCESS]" + colors.reset + " " + colors.bright + tag + colors.reset + ": " + message);
    }

    function logWarning(tag, message) {
        console.log(colors.yellow + "[WARNING]" + colors.reset + " " + colors.bright + tag + colors.reset + ": " + message);
    }

    function logError(tag, message) {
        console.log(colors.red + "[ERROR]" + colors.reset + " " + colors.bright + tag + colors.reset + ": " + message);
    }

    function safeGetString(obj) {
        try {
            if (obj === null || obj === undefined) return "null";
            return obj.toString();
        } catch (e) {
            return "Error getting string: " + e.message;
        }
    }

    function getByteStringContent(byteString) {
        try {
            if (byteString === null || byteString === undefined) return "null";
            return byteString.utf8();
        } catch (e) {
            try {
                return byteString.toString();
            } catch (e2) {
                return "Error reading ByteString: " + e2.message;
            }
        }
    }

    function hookMethodSafely(className, methodName, hookFunction, description) {
        try {
            const targetClass = Java.use(className);
            const method = targetClass[methodName];
            
            if (method.overloads && method.overloads.length > 1) {
                logInfo("HOOK", `${description} has ${method.overloads.length} overloads, hooking all`);
                method.overloads.forEach((overload, index) => {
                    overload.implementation = hookFunction;
                });
            } else {
                method.implementation = hookFunction;
            }
            
            logSuccess("HOOK", `${description} hooked successfully`);
            return true;
        } catch (e) {
            logWarning("HOOK", `Failed to hook ${description}: ${e.message}`);
            return false;
        }
    }

    // 1. Hook SetUserInfo Builder - Core FCM Token Tracking
    try {
        const SetUserInfoBuilder = Java.use("com.shopee.protocol.action.SetUserInfo$Builder");
        
        // Hook constructors
        if (SetUserInfoBuilder.$init.overloads) {
            SetUserInfoBuilder.$init.overloads.forEach((overload, index) => {
                overload.implementation = function() {
                    logInfo("BUILDER", `SetUserInfo Builder created (constructor ${index})`);
                    return this.$init.apply(this, arguments);
                };
            });
        }

        // Hook key methods
        hookMethodSafely("com.shopee.protocol.action.SetUserInfo$Builder", "pn_token", function(token) {
            const tokenContent = getByteStringContent(token);
            logSuccess("FCM_TOKEN", "FCM token set: " + tokenContent.substring(0, 50) + "...");
            console.log("    Token length: " + (token ? token.size() : 0));
            return this.pn_token(token);
        }, "SetUserInfo Builder pn_token");

        hookMethodSafely("com.shopee.protocol.action.SetUserInfo$Builder", "machine_code", function(machineCode) {
            logSuccess("MACHINE_CODE", "Machine code set: " + safeGetString(machineCode));
            return this.machine_code(machineCode);
        }, "SetUserInfo Builder machine_code");

        hookMethodSafely("com.shopee.protocol.action.SetUserInfo$Builder", "build", function() {
            logSuccess("BUILD", "Building SetUserInfo protocol buffer");
            const result = this.build();
            return result;
        }, "SetUserInfo Builder build");

        logSuccess("HOOK", "SetUserInfo Builder hooks installed");
    } catch (e) {
        logError("HOOK", "Failed to hook SetUserInfo Builder: " + e.message);
    }

    // 2. Hook Network Request Classes
    const requestClasses = ["z", "x", "y"];
    
    requestClasses.forEach(className => {
        try {
            const RequestClass = Java.use("com.shopee.app.network.request." + className);
            
            // Hook TCP packet creation method
            hookMethodSafely("com.shopee.app.network.request." + className, "d", function() {
                logSuccess("TCP_PACKET", `Creating TCP packet for request class ${className}`);
                const result = this.d();
                
                if (result && result.b() === 0x43) {
                    console.log("    Command: 0x43 (SetUserInfo)");
                    console.log("    Payload size: " + (result.c() ? result.c().length : 0) + " bytes");
                }
                
                return result;
            }, `Request class ${className} TCP packet creation`);

            logSuccess("HOOK", `Request class ${className} hooked`);
        } catch (e) {
            logWarning("HOOK", `Could not hook request class ${className}: ${e.message}`);
        }
    });

    // 3. Hook FCM Token Registration Entry Point
    hookMethodSafely("com.shopee.app.pushnotification.g", "e", function(pushToken, userInfo, deviceStore, machineCode) {
        logSuccess("FCM_REGISTER", "FCM Token Registration Called");
        console.log("    Push Token: " + safeGetString(pushToken).substring(0, 50) + "...");
        console.log("    Machine Code: " + safeGetString(machineCode));
        
        if (userInfo) {
            try {
                console.log("    User logged in: " + userInfo.isLoggedIn());
                if (userInfo.isLoggedIn()) {
                    console.log("    User ID: " + userInfo.getUserId());
                }
            } catch (e) {
                logWarning("FCM_REGISTER", "Could not get user info: " + e.message);
            }
        }
        
        return this.e(pushToken, userInfo, deviceStore, machineCode);
    }, "FCM Token Registration");

    // 4. Hook Network Manager TCP Send
    hookMethodSafely("com.shopee.app.network.e", "p", function(tcpPacket, requestId, userId, wireMessage) {
        if (tcpPacket && tcpPacket.b() === 0x43) {
            logSuccess("NETWORK", "Sending SetUserInfo TCP packet (0x43)");
            console.log("    Request ID: " + safeGetString(requestId));
            console.log("    User ID: " + safeGetString(userId));
            console.log("    Packet size: " + (tcpPacket.c() ? tcpPacket.c().length : 0) + " bytes");
        }
        
        return this.p(tcpPacket, requestId, userId, wireMessage);
    }, "Network Manager TCP send");

    // 5. Hook FCM Service Token Update
    hookMethodSafely("com.shopee.app.pushnotification.fcm.ShopeeFcmMessageService", "onNewToken", function(token) {
        logSuccess("FCM_NEW_TOKEN", "New FCM token received");
        console.log("    Token: " + safeGetString(token).substring(0, 50) + "...");
        console.log("    Token length: " + (token ? token.length : 0));
        
        return this.onNewToken(token);
    }, "FCM Service onNewToken");

    // 6. Hook SetUserInfo Response Processor
    hookMethodSafely("com.shopee.app.network.processors.l0", "i", function(responseBytes, length) {
        logSuccess("RESPONSE", "SetUserInfo response received");
        console.log("    Response length: " + length + " bytes");
        
        const result = this.i(responseBytes, length);
        
        // Try to parse response
        try {
            const Wire = Java.use("com.squareup.wire.Wire");
            const ResponseCommon = Java.use("com.shopee.protocol.action.ResponseCommon");
            const wire = Wire.a.value;
            const response = wire.parseFrom(responseBytes, 0, length, ResponseCommon.class);
            
            if (response) {
                console.log("    Request ID: " + safeGetString(response.requestid.value));
                console.log("    Error code: " + (response.errcode.value ? response.errcode.value : "0"));
                console.log("    Success: " + (response.errcode.value === 0));
            }
        } catch (e) {
            logWarning("RESPONSE", "Could not parse response: " + e.message);
        }
        
        return result;
    }, "SetUserInfo Response Processor");

    // 7. Hook Login Success Callbacks
    hookMethodSafely("com.shopee.app.web.processor.WebDidFinishToCLoginProcessor", "sendUserData", function() {
        logSuccess("LOGIN_SUCCESS", "ToC Login success - sending user data");
        console.log("    This will trigger FCM token registration");
        
        return this.sendUserData();
    }, "ToC Login Success");

    // 8. Hook Device Store FCM Token Storage
    hookMethodSafely("com.shopee.app.data.store.j0", "N", function(token) {
        logSuccess("STORAGE", "Storing FCM token in device store");
        console.log("    Token: " + safeGetString(token).substring(0, 50) + "...");
        
        return this.N(token);
    }, "Device Store FCM token storage");

    // 9. Hook User Authentication State Changes
    hookMethodSafely("com.shopee.app.appuser.UserInfo", "setToken", function(token) {
        logSuccess("USER_AUTH", "User authentication token set");
        console.log("    Token: " + safeGetString(token).substring(0, 50) + "...");
        
        return this.setToken(token);
    }, "UserInfo setToken");

    hookMethodSafely("com.shopee.app.appuser.UserInfo", "setUserId", function(userId) {
        logSuccess("USER_ID", "User ID set: " + userId);
        
        return this.setUserId(userId);
    }, "UserInfo setUserId");

    // Summary function
    function printCurrentState() {
        console.log(colors.magenta + "\n=== CURRENT STATE SUMMARY ===" + colors.reset);
        
        try {
            const app = Java.use("com.shopee.app.application.ShopeeApplication").get();
            const userComponent = app.getComponent();
            const userInfo = userComponent.loggedInUser();
            
            console.log("User logged in: " + userInfo.isLoggedIn());
            if (userInfo.isLoggedIn()) {
                console.log("User ID: " + userInfo.getUserId());
            }
            
            const deviceStore = userComponent.deviceStore();
            const storedToken = safeGetString(deviceStore.j());
            console.log("Stored FCM token: " + (storedToken.length > 50 ? storedToken.substring(0, 50) + "..." : storedToken));
            console.log("Stored machine code: " + safeGetString(deviceStore.i()));
            
        } catch (e) {
            logWarning("STATE", "Could not get current state: " + e.message);
        }
        
        console.log(colors.magenta + "==============================\n" + colors.reset);
    }

    // Make printCurrentState available globally
    global.printCurrentState = printCurrentState;

    // Print initial state after a delay
    setTimeout(printCurrentState, 3000);

    console.log(colors.green + "[+] Simplified SetUserInfo FCM Token Logger initialized!" + colors.reset);
    console.log(colors.yellow + "[*] Monitoring FCM token registration and SetUserInfo usage..." + colors.reset);
    console.log(colors.cyan + "[*] Use 'printCurrentState()' in console to see current state" + colors.reset);
});
